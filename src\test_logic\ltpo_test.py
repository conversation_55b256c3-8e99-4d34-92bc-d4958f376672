"""
LTPO测试逻辑模块
实现LTPO设备的灰阶测试功能
"""

import time
import random
from collections import Counter
from typing import List, Dict, Any, Optional
from .base_test import BaseTest
from ..utils.constants import MIN_BRIGHTNESS, MAX_BRIGHTNESS
from ..utils.logger import get_logger
from src.core.adb_controller import ADBController


class LTPoTest(BaseTest):
    """LTPO测试类"""
    
    def __init__(self, desktop_test: bool = False, enable_inspection: bool = True):
        """
        初始化LTPO测试
        
        Args:
            desktop_test: 是否为桌面测试
            enable_inspection: 是否启用灰阶校验
        """
        super().__init__(desktop_test, enable_inspection)
        self.test_type = "LTPO"
        self.adb_controller = ADBController()

        # 设置数据采集模式为 LTPO，确保预热与运行时优先走 LTPO 的 FPS 获取
        try:
            self.data_collector.set_mode('ltpo')
        except Exception:
            pass
    
    def get_test_type(self) -> str:
        """获取测试类型"""
        return self.test_type
    
    def get_fps_value_most(self, sample_count: Optional[int] = None,
                          sample_interval: Optional[float] = None,
                          enable_detailed_stats: bool = True) -> Optional[int]:
        """
        获取出现次数最多的FPS值（优化版）

        Args:
            sample_count: 采样次数，None时使用配置值
            sample_interval: 采样间隔（秒），None时使用配置值
            enable_detailed_stats: 是否启用详细统计信息

        Returns:
            最常见的FPS值
        """
        try:
            # 获取配置参数
            if sample_count is None:
                sample_count = self.config.get('test_settings.ltpo.fps_sample_count', 4)
            if sample_interval is None:
                sample_interval = self.config.get('test_settings.ltpo.fps_sample_interval', 0.1)

            # 参数验证
            sample_count = max(3, min(15, sample_count))  # 限制在3-15次之间
            sample_interval = max(0.1, min(1.0, sample_interval))  # 限制在0.1-1.0秒之间

            fps_samples = []
            failed_samples = 0

            if self.logger and enable_detailed_stats:
                self.logger.debug(f"开始FPS采样：次数={sample_count}, 间隔={sample_interval}s")

            for i in range(sample_count):
                try:
                    data = self.data_collector.get_current_data()
                    fps = data.get('fps')

                    if fps is not None and fps > 0:
                        fps_samples.append(fps)
                        if self.logger and enable_detailed_stats:
                            self.logger.debug(f"采样{i+1}: FPS={fps}")
                    else:
                        failed_samples += 1
                        if self.logger:
                            self.logger.debug(f"采样{i+1}: 获取FPS失败")

                    if i < sample_count - 1:
                        time.sleep(sample_interval)

                except Exception as e:
                    failed_samples += 1
                    if self.logger:
                        self.logger.debug(f"采样{i+1}异常: {e}")
                    continue

            if not fps_samples:
                if self.logger:
                    self.logger.warning(f"所有FPS采样都失败，失败次数: {failed_samples}")
                return None

            # 统计分析
            fps_counter = Counter(fps_samples)
            most_common_fps, most_common_count = fps_counter.most_common(1)[0]

            # 计算置信度
            confidence = most_common_count / len(fps_samples)
            confidence_threshold = self.config.get('test_settings.ltpo.fps_confidence_threshold', 0.6)

            # 详细统计信息
            if self.logger and enable_detailed_stats:
                unique_fps = list(fps_counter.keys())
                fps_distribution = {fps: count for fps, count in fps_counter.items()}

                self.logger.info(f"FPS采样统计: 总采样={len(fps_samples)}, 失败={failed_samples}")
                self.logger.info(f"FPS分布: {fps_distribution}")
                self.logger.info(f"最常见FPS: {most_common_fps} (出现{most_common_count}次, 置信度={confidence:.2f})")

                if confidence < confidence_threshold:
                    self.logger.warning(f"FPS置信度({confidence:.2f})低于阈值({confidence_threshold})")

            return most_common_fps

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取FPS值失败: {e}")
            return None
    
    def test_brightness_gradient(self) -> List[Dict[str, Any]]:
        """
        执行亮度梯度测试
        
        Returns:
            亮度测试数据列表
        """
        if self.logger:
            self.logger.info("开始LTPO亮度梯度测试...")

        try:
            # 关闭当前应用
            self.adb_controller.stop_current_app()
            time.sleep(3)
            # 打开黑色灰阶图片
            self.image_controller.open_grey_image(0)
            
            brightness_data = []
            
            # 遍历所有亮度值
            for brightness in range(MIN_BRIGHTNESS, MAX_BRIGHTNESS + 1):
                # 设置亮度
                self.device_manager.set_brightness(brightness)
                
                # 每10个亮度值重置屏幕策略
                if brightness % 10 == 0:
                    self.device_manager.reset_screen_policy()
                
                time.sleep(self.wait_time)
                
                # 获取当前数据
                current_fps = self.get_fps_value_most()
                current_data = self.data_collector.get_current_data()
                nit_value = current_data.get('nit', 0)
                
                # 记录数据
                data_point = {
                    "nit": nit_value,
                    "fps": current_fps,
                    "brightness": brightness
                }
                brightness_data.append(data_point)
                
                if self.logger:
                    self.logger.info(f"亮度 {brightness}: {data_point}")
                
                # 如果FPS降到1或更低，停止测试
                if current_fps and current_fps <= 1:
                    if self.logger:
                        self.logger.info(f"FPS降至 {current_fps}，停止亮度梯度测试")
                    break
            
            self.brightness_data = brightness_data
            return brightness_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"亮度梯度测试失败: {e}")
            return []
    
    def test_grey_scale_threshold(self) -> List[Dict[str, Any]]:
        """
        执行灰阶门限测试
        
        Returns:
            灰阶测试数据列表
        """
        if self.logger:
            self.logger.info("开始LTPO灰阶门限测试...")
        
        try:
            grey_data = []
            
            # 获取亮度数据中的唯一FPS值
            fps_values = list(set(item.get('fps', 0) for item in self.brightness_data if item.get('fps')))
            fps_values.sort()
            
            if self.logger:
                self.logger.info(f"检测到的FPS值: {fps_values}")
            
            for target_fps in fps_values:
                if target_fps <= 0:
                    continue
                
                # 找到对应FPS的亮度范围
                brightness_range = self._get_brightness_range_for_fps(target_fps)
                if not brightness_range:
                    continue
                
                min_brightness, max_brightness = brightness_range
                test_brightness = (min_brightness + max_brightness) // 2
                
                if self.logger:
                    self.logger.info(f"测试FPS {target_fps}，使用亮度 {test_brightness}")
                
                # 在该亮度下测试灰阶门限
                grey_threshold = self._find_grey_threshold(test_brightness, target_fps)
                
                if grey_threshold is not None:
                    grey_data.append(grey_threshold)
            
            self.grey_data = grey_data
            return grey_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"灰阶门限测试失败: {e}")
            return []
    
    def _get_brightness_range_for_fps(self, target_fps: int) -> Optional[tuple]:
        """
        获取指定FPS对应的亮度范围
        
        Args:
            target_fps: 目标FPS
            
        Returns:
            (最小亮度, 最大亮度) 或 None
        """
        try:
            matching_items = [
                item for item in self.brightness_data 
                if item.get('fps') == target_fps
            ]
            
            if not matching_items:
                return None
            
            brightness_values = [item.get('brightness', 0) for item in matching_items]
            return min(brightness_values), max(brightness_values)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"获取亮度范围失败: {e}")
            return None
    
    def _find_grey_threshold(self, brightness: int, target_fps: int) -> Optional[Dict[str, Any]]:
        """
        查找灰阶门限
        
        Args:
            brightness: 测试亮度
            target_fps: 目标FPS
            
        Returns:
            灰阶门限数据
        """
        try:
            # 设置亮度
            self.device_manager.set_brightness(brightness)
            
            # 二分查找灰阶门限
            low, high = 0, 64
            threshold_index = None
            
            while low <= high:
                mid = (low + high) // 2
                
                # 打开灰阶图片
                self.image_controller.open_grey_image(mid)
                time.sleep(self.wait_time)
                
                # 获取当前FPS
                current_fps = self.get_fps_value_most()

                time.sleep(2)

                # 关闭当前应用
                self.adb_controller.stop_current_app()

                if self.logger:
                    self.logger.debug(f"灰阶 {mid}: FPS {current_fps}")
                
                if current_fps == target_fps:
                    threshold_index = mid
                    low = mid + 1
                else:
                    high = mid - 1
            
            if threshold_index is not None:
                # 获取门限点的详细数据
                self.image_controller.open_grey_image(threshold_index)
                time.sleep(self.wait_time)
                
                current_data = self.data_collector.get_current_data()
                
                return {
                    "fps": target_fps,
                    "grey_value": threshold_index,
                    "index": threshold_index,
                    "brightness": brightness,
                    "nit": current_data.get('nit', 0),
                    "range": brightness
                }
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"查找灰阶门限失败: {e}")
            return None
    
    def run_test(self) -> bool:
        """
        运行LTPO测试
        
        Returns:
            测试是否成功
        """
        try:
            if self.logger:
                self.logger.info("开始执行LTPO灰阶测试...")

            # 设置测试网络环境
            if self.logger:
                self.logger.info("设置测试网络环境...")
            if not self.device_manager.setup_test_network_environment():
                if self.logger:
                    self.logger.warning("设置测试网络环境失败，继续测试...")

            # 1. 执行亮度梯度测试
            brightness_data = self.test_brightness_gradient()
            if not brightness_data:
                if self.logger:
                    self.logger.error("亮度梯度测试失败")
                return False

            if self.logger:
                self.logger.info(f"亮度梯度测试完成，获得 {len(brightness_data)} 个数据点")
            
            # 2. 执行灰阶门限测试
            grey_data = self.test_grey_scale_threshold()
            if not grey_data:
                if self.logger:
                    self.logger.warning("灰阶门限测试未获得数据")
            else:
                if self.logger:
                    self.logger.info(f"灰阶门限测试完成，获得 {len(grey_data)} 个数据点")
            
            # 3. 合并数据
            if brightness_data and grey_data:
                self.test_results = self.data_processor.merge_brightness_grey_data(
                    brightness_data, grey_data
                )
            else:
                # 如果没有灰阶数据，只使用亮度数据
                self.test_results = brightness_data
            
            if self.logger:
                self.logger.info("LTPO测试执行完成")
            
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"LTPO测试执行失败: {e}")
            return False
        finally:
            # 确保在测试结束后恢复网络环境
            try:
                if self.logger:
                    self.logger.info("恢复网络环境...")
                self.device_manager.restore_network_environment()
            except Exception as restore_error:
                if self.logger:
                    self.logger.warning(f"恢复网络环境失败: {restore_error}")


def run_ltpo_test(desktop_test: bool = False, enable_inspection: bool = True) -> bool:
    """
    运行LTPO测试的便捷函数
    
    Args:
        desktop_test: 是否为桌面测试
        enable_inspection: 是否启用灰阶校验
        
    Returns:
        测试是否成功
    """
    test = LTPoTest(desktop_test=desktop_test, enable_inspection=enable_inspection)
    return test.execute_test()
