"""
日志数据收集模块
负责从设备收集测试数据
"""

import re
import subprocess
import threading
import time
from queue import Queue
from typing import Optional, List, Dict, Any
from src.utils.config import config_manager

# 处理导入问题 - 支持相对导入和绝对导入
try:
    # 尝试相对导入（在包内运行时）
    from ..core.adb_controller import ADBController
    from ..utils.constants import ERROR_MESSAGES
    from ..utils.logger import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入（独立运行时）
    import sys
    import os

    # 添加项目根目录到路径
    current_file = os.path.abspath(__file__)
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    try:
        from src.core.adb_controller import ADBController
        from src.utils.constants import ERROR_MESSAGES
        from src.utils.logger import get_logger
    except ImportError:
        # 如果仍然失败，创建简化版本
        pass  # 静默处理导入失败

        class ADBController:
            def __init__(self):
                pass
            def set_brightness(self, brightness):
                subprocess.run(f"adb shell settings put system screen_brightness {brightness}", shell=True)

        ERROR_MESSAGES = {
            'NO_BRIGHTNESS_VALUE': '无法获取到亮度值，请确认设备状态...'
        }

        def get_logger():
            return None


class LogDataCollector:
    """日志数据收集器"""
    
    def __init__(self):
        self.log_queue = Queue()
        self.nit_current_value = 0
        self.adb = ADBController()
        self.logger = get_logger()
        self._stop_event = threading.Event()
        self._thread = None
    
    def start_collection(self) -> None:
        """开始数据收集"""
        if self._thread and self._thread.is_alive():
            if self.logger:
                self.logger.warning("数据收集已在运行")
            return
        
        self._stop_event.clear()
        self._thread = threading.Thread(target=self._collect_nit_data, daemon=True)
        self._thread.start()
        
        if self.logger:
            self.logger.info("开始收集日志数据")
    
    def stop_collection(self) -> None:
        """停止数据收集"""
        if self._thread and self._thread.is_alive():
            self._stop_event.set()
            self._thread.join(timeout=5)
            
        if self.logger:
            self.logger.info("停止收集日志数据")
    
    def _collect_nit_data(self) -> None:
        """从日志中收集NIT数据"""
        try:
            # 使用更安全的命令执行方式
            import platform

            if platform.system() == "Windows":
                # Windows系统使用不同的命令格式
                cmd = 'adb shell "logcat | grep VivoBrightnessPolicy"'
                encoding = 'gbk'
            else:
                cmd = ["adb", "shell", "logcat", "|", "grep", "VivoBrightnessPolicy"]
                encoding = 'utf-8'

            with subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                shell=True, encoding=encoding, errors='ignore') as process:

                while not self._stop_event.is_set():
                    try:
                        line = process.stdout.readline()
                        if not line:
                            break

                        # 确保字符串编码正确
                        if isinstance(line, bytes):
                            try:
                                line = line.decode('utf-8', errors='ignore')
                            except:
                                line = line.decode('gbk', errors='ignore')

                        if "Get target percent" in line:
                            self.log_queue.put(line.strip())

                    except Exception as line_error:
                        if self.logger:
                            self.logger.debug(f"处理日志行时出错: {line_error}")
                        continue

        except Exception as e:
            if self.logger:
                self.logger.error(f"收集NIT数据失败: {e}")
    
    def extract_nit_value(self, log_line: str) -> Optional[str]:
        """
        从日志行中提取NIT值
        
        Args:
            log_line: 日志行
            
        Returns:
            NIT值字符串
        """
        match = re.search(r'target nit (\d+)', log_line)
        if match:
            return match.group(1)
        return None
    
    def get_nit_value(self) -> int:
        """
        获取当前NIT值
        
        Returns:
            NIT值
            
        Raises:
            ValueError: 无法获取NIT值时抛出
        """
        last_nit_data = None
        
        # 从队列中获取最新的NIT数据
        while not self.log_queue.empty():
            last_nit_data = self.log_queue.get()
        
        if last_nit_data:
            nit_value = self.extract_nit_value(last_nit_data)
            if nit_value:
                self.nit_current_value = int(nit_value)
        
        if self.nit_current_value == 0:
            raise ValueError(ERROR_MESSAGES['NO_NIT_VALUE'])
        
        return self.nit_current_value
    
    def get_fps_value(self) -> Optional[int]:
        """
        获取当前FPS值
        
        Returns:
            FPS值
        """
        try:
            # 使用ADB获取FPS
            result = self.adb.execute_command(
                "adb shell dumpsys SurfaceFlinger | grep 'refresh-rate'", 
                capture_output=True
            )
            
            if result:
                # 解析FPS值
                match = re.search(r'(\d+\.?\d*)', result)
                if match:
                    return int(float(match.group(1)))
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"获取FPS值失败: {e}")
            return None


class FPSCollector:
    """FPS数据收集器"""
    
    def __init__(self):
        self.adb = ADBController()
        self.logger = get_logger()
    
    def get_fps_value(self, use_multiple_samples: bool = False,
                     sample_count: int = 8,
                     sample_interval: float = 0.25,
                     enable_stats: bool = False) -> Optional[int]:
        """
        获取FPS值

        Args:
            use_multiple_samples: 是否使用多次采样
            sample_count: 采样次数（仅在use_multiple_samples=True时有效）
            sample_interval: 采样间隔（秒）
            enable_stats: 是否启用统计信息

        Returns:
            FPS值
        """
        if use_multiple_samples:
            return self._get_fps_with_samples(sample_count, sample_interval, enable_stats)
        else:
            return self._get_single_fps()
    
    def _get_single_fps(self) -> Optional[int]:
        """获取单次FPS值"""
        try:
            # 方法1: 通过dumpsys SurfaceFlinger
            result = self.adb.execute_command(
                "adb shell dumpsys SurfaceFlinger | grep refresh-rate",
                capture_output=True
            )

            if result:
                match = re.search(r'(\d+\.?\d*)', result)
                if match:
                    fps = int(float(match.group(1)))
                    if self.logger:
                        self.logger.debug(f"通过SurfaceFlinger获取FPS: {fps}")
                    return fps

            # 方法2: 通过dumpsys display
            result = self.adb.execute_command(
                "adb shell dumpsys display | grep mRefreshRate",
                capture_output=True
            )

            if result:
                match = re.search(r'mRefreshRate=(\d+\.?\d*)', result)
                if match:
                    fps = int(float(match.group(1)))
                    if self.logger:
                        self.logger.debug(f"通过display获取FPS: {fps}")
                    return fps

            # 方法3: 备用方法
            result = self.adb.execute_command(
                "adb shell getprop debug.sf.frame_rate_multiple_threshold",
                capture_output=True
            )

            if result and result.isdigit():
                fps = int(result)
                if self.logger:
                    self.logger.debug(f"通过属性获取FPS: {fps}")
                return fps

            if self.logger:
                self.logger.warning("所有FPS获取方法都失败")
            return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取FPS值失败: {e}")
            return None
    
    def _get_fps_with_samples(self, sample_count: int = 8,
                             sample_interval: float = 0.25,
                             enable_stats: bool = False) -> Optional[int]:
        """
        通过多次采样获取FPS值（优化版）

        Args:
            sample_count: 采样次数
            sample_interval: 采样间隔（秒）
            enable_stats: 是否启用统计信息

        Returns:
            最常见的FPS值
        """
        from collections import Counter
        import statistics

        fps_samples = []
        failed_count = 0

        if self.logger and enable_stats:
            self.logger.debug(f"开始多次FPS采样: 次数={sample_count}, 间隔={sample_interval}s")

        for i in range(sample_count):
            try:
                fps = self._get_single_fps()
                if fps is not None and fps > 0:
                    fps_samples.append(fps)
                    if self.logger and enable_stats:
                        self.logger.debug(f"FPS采样{i+1}: {fps}")
                else:
                    failed_count += 1
                    if self.logger and enable_stats:
                        self.logger.debug(f"FPS采样{i+1}: 失败")

                if i < sample_count - 1:
                    time.sleep(sample_interval)

            except Exception as e:
                failed_count += 1
                if self.logger:
                    self.logger.debug(f"FPS采样{i+1}异常: {e}")
                continue

        if not fps_samples:
            if self.logger:
                self.logger.warning(f"所有FPS采样失败，失败次数: {failed_count}")
            return None

        # 统计分析
        fps_counter = Counter(fps_samples)
        most_common_fps, most_common_count = fps_counter.most_common(1)[0]

        # 详细统计（如果启用）
        if self.logger and enable_stats and len(fps_samples) > 1:
            try:
                mean_fps = statistics.mean(fps_samples)
                median_fps = statistics.median(fps_samples)
                if len(fps_samples) > 1:
                    stdev_fps = statistics.stdev(fps_samples)
                else:
                    stdev_fps = 0

                confidence = most_common_count / len(fps_samples)
                unique_values = len(fps_counter)

                self.logger.debug(f"FPS采样结果统计:")
                self.logger.debug(f"  - 有效采样: {len(fps_samples)}/{sample_count}")
                self.logger.debug(f"  - 平均值: {mean_fps:.1f}, 中位数: {median_fps}")
                self.logger.debug(f"  - 标准差: {stdev_fps:.2f}, 唯一值数量: {unique_values}")
                self.logger.debug(f"  - 最常见值: {most_common_fps} (置信度: {confidence:.2f})")
                self.logger.debug(f"  - 分布: {dict(fps_counter)}")

            except Exception as e:
                if self.logger:
                    self.logger.debug(f"统计计算失败: {e}")

        return most_common_fps
    
    def get_ltpo_fps(self, use_multiple_samples: bool = True,
                    sample_count: int = 8,
                    sample_interval: float = 0.25) -> Optional[int]:
        """
        获取LTPO设备的FPS值（优化版）

        Args:
            use_multiple_samples: 是否使用多次采样
            sample_count: 采样次数
            sample_interval: 采样间隔（秒）

        Returns:
            FPS值
        """
        try:
            if use_multiple_samples:
                # 使用多次采样获取更稳定的LTPO FPS值
                fps_samples = []
                failed_count = 0

                for i in range(sample_count):
                    try:
                        # LTPO设备特定的FPS获取方法
                        result = self.adb.execute_command(
                            "adb shell cat /sys/lcm/refresh_monitor",
                            capture_output=True
                        )

                        fps = None
                        if result:
                            match = re.search(r'(\d+)', result)
                            if match:
                                fps = int(match.group(1))

                        # 如果LTPO特定方法失败，回退到通用方法
                        if fps is None:
                            fps = self._get_single_fps()

                        if fps is not None and fps > 0:
                            fps_samples.append(fps)
                        else:
                            failed_count += 1

                        if i < sample_count - 1:
                            time.sleep(sample_interval)

                    except Exception as e:
                        failed_count += 1
                        if self.logger:
                            self.logger.debug(f"LTPO FPS采样{i+1}失败: {e}")
                        continue

                if not fps_samples:
                    if self.logger:
                        self.logger.warning(f"所有LTPO FPS采样失败，失败次数: {failed_count}")
                    return None

                # 返回出现次数最多的FPS值
                from collections import Counter
                fps_counter = Counter(fps_samples)
                most_common_fps = fps_counter.most_common(1)[0][0]

                if self.logger:
                    self.logger.debug(f"LTPO FPS多次采样结果: {dict(fps_counter)}, 最终值: {most_common_fps}")

                return most_common_fps
            else:
                # 单次采样
                result = self.adb.execute_command(
                    "adb shell cat /sys/lcm/refresh_monitor",
                    capture_output=True
                )

                if result:
                    match = re.search(r'(\d+)', result)
                    if match:
                        return int(match.group(1))

                # 回退到通用方法
                return self._get_single_fps()

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取LTPO FPS值失败: {e}")
            return self._get_single_fps()


class DataCollectionManager:
    """数据收集管理器"""
    
    def __init__(self):
        self.log_collector = LogDataCollector()
        self.fps_collector = FPSCollector()
        self.logger = get_logger()
        self.config = config_manager
        # 预热与缓存
        self._monitoring = False
        self._ready_event = threading.Event()
        self._lock = threading.Lock()
        self._last_nit: Optional[int] = None
        self._last_fps: Optional[int] = None
        self._last_ts: float = 0.0
        # 采集模式：'ltps' 或 'ltpo'
        self._mode: str = 'ltps'

    def set_mode(self, mode: str) -> None:
        """设置采集模式（ltps/ltpo）"""
        if mode not in ('ltps', 'ltpo'):
            raise ValueError("mode 必须是 'ltps' 或 'ltpo'")
        with self._lock:
            self._mode = mode
    
    def start_collection(self) -> None:
        """开始数据收集"""
        self.log_collector.start_collection()
        if self.logger:
            self.logger.info("数据收集管理器已启动")
    
    def stop_collection(self) -> None:
        """停止数据收集"""
        self.log_collector.stop_collection()
        if self.logger:
            self.logger.info("数据收集管理器已停止")
    
    def get_current_data(self) -> Dict[str, Any]:
        """
        获取当前数据
        
        Returns:
            包含NIT和FPS的数据字典
        """
        nit_value: Optional[int] = None
        fps_value: Optional[int] = None

        # 分别采集，互不影响
        try:
            nit_value = self.log_collector.get_nit_value()
        except Exception as e:
            if self.logger:
                self.logger.debug(f"本次获取NIT失败: {e}")
        try:
            # 获取配置参数
            sample_count = None
            sample_interval = None
            if sample_count is None:
                sample_count = self.config.get('test_settings.ltpo.fps_sample_count', 4)
            if sample_interval is None:
                sample_interval = self.config.get('test_settings.ltpo.fps_sample_interval', 0.1)

            # 按模式获取FPS，LTPO模式默认使用多次采样
            if self._mode == 'ltpo':
                fps_value = self.fps_collector.get_ltpo_fps(
                    use_multiple_samples=True,
                    sample_count=sample_count,
                    sample_interval=sample_interval
                )
            else:
                fps_value = self.fps_collector.get_fps_value()
        except Exception as e:
            if self.logger:
                self.logger.debug(f"本次获取FPS失败: {e}")

        # 回退到最近有效值并更新缓存
        with self._lock:
            if nit_value is None:
                nit_value = self._last_nit
            else:
                self._last_nit = nit_value

            if fps_value is None:
                fps_value = self._last_fps
            else:
                self._last_fps = fps_value

            ts = time.time()
            self._last_ts = ts

        return {
            'nit': nit_value,
            'fps': fps_value,
            'timestamp': ts,
        }

    def start_monitoring(self, prewarm_timeout: float = 10.0,
                         require_nit: bool = True,
                         require_fps: bool = True) -> None:
        """
        启动预热监控：确保logcat线程已启动，并尽快采集到至少一次nit/fps。
        幂等：重复调用无副作用。
        """
        # 启动logcat采集线程（若未启动）
        self.start_collection()

        if self._monitoring:
            return

        self._monitoring = True
        self._ready_event.clear()

        # 立即进行一次FPS尝试，提升首次可用性
        try:
            # 首次FPS尝试按模式分流，LTPO模式使用快速单次采样进行预热
            if self._mode == 'ltpo':
                first_fps = self.fps_collector.get_ltpo_fps(use_multiple_samples=False)
                if first_fps is None:
                    # 回退通用
                    first_fps = self.fps_collector.get_fps_value()
            else:
                first_fps = self.fps_collector.get_fps_value()
            if first_fps is not None:
                with self._lock:
                    self._last_fps = first_fps
        except Exception as e:
            if self.logger:
                self.logger.debug(f"预热首次FPS尝试失败: {e}")

        # 轮询直到就绪或超时
        start = time.time()
        while time.time() - start < prewarm_timeout:
            data = self.get_current_data()
            nit_ok = (data.get('nit') is not None) if require_nit else True
            fps_ok = (data.get('fps') is not None) if require_fps else True
            if nit_ok and fps_ok:
                self._ready_event.set()
                if self.logger:
                    self.logger.info(f"数据预热完成: nit={data.get('nit')}, fps={data.get('fps')}")
                return
            time.sleep(0.3)
        # 超时未就绪也继续运行
        if self.logger:
            self.logger.warning("数据预热超时，后续将使用回退与持续采样")

    def wait_until_ready(self, timeout: float = 10.0) -> bool:
        """阻塞等待预热完成，返回是否在超时内就绪"""
        return self._ready_event.wait(timeout=timeout)

    def stop_monitoring(self) -> None:
        """停止监控并清理状态"""
        self.stop_collection()
        with self._lock:
            self._monitoring = False
        self._ready_event.clear()
    
    def collect_brightness_data(self, brightness: int) -> Dict[str, Any]:
        """
        收集指定亮度下的数据
        
        Args:
            brightness: 亮度值
            
        Returns:
            数据字典
        """
        try:
            data = self.get_current_data()
            data['brightness'] = brightness
            return data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"收集亮度数据失败: {e}")
            return {'brightness': brightness, 'nit': 0, 'fps': 0}
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_collection()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_collection()




