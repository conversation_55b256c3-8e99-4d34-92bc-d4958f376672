"""
配置管理模块
负责加载和管理项目配置
"""

import json
import os
from typing import Dict, Any, Optional
from .constants import CONFIG_DIR, DEFAULT_CONFIG


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "default.json"):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件名
        """
        self.config_file = os.path.join(CONFIG_DIR, config_file)
        self._config = {}
        self.load_config()
        # 确保配置完整性
        self.ensure_config_integrity()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 验证加载的配置是否有效
                    if isinstance(loaded_config, dict) and loaded_config:
                        self._config = loaded_config
                        print(f"配置文件加载成功: {self.config_file}")
                    else:
                        print(f"配置文件格式无效，使用默认配置")
                        self._config = DEFAULT_CONFIG.copy()
                        self.save_config()
            else:
                # 如果配置文件不存在，使用默认配置
                print(f"配置文件不存在，创建默认配置: {self.config_file}")
                self._config = DEFAULT_CONFIG.copy()
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            print("使用默认配置")
            self._config = DEFAULT_CONFIG.copy()
            # 尝试保存默认配置
            try:
                self.save_config()
            except Exception as save_error:
                print(f"保存默认配置失败: {save_error}")
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            os.makedirs(CONFIG_DIR, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        self._config.update(config_dict)
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()

    def validate_config(self) -> bool:
        """
        验证配置完整性

        Returns:
            配置是否有效
        """
        try:
            # 检查关键配置项
            required_keys = [
                'test_timeout',
                'retry_count',
                'wait_time',
                'test_settings.ltpo.fps_sample_count',
                'test_settings.ltpo.fps_sample_interval',
                'test_settings.ltpo.fps_confidence_threshold'
            ]

            for key in required_keys:
                value = self.get(key)
                if value is None:
                    print(f"缺少必需的配置项: {key}")
                    return False

            return True

        except Exception as e:
            print(f"配置验证失败: {e}")
            return False

    def ensure_config_integrity(self) -> None:
        """确保配置完整性，如果缺少关键配置则补充默认值"""
        try:
            if not self.validate_config():
                print("配置不完整，正在补充默认值...")

                # 合并默认配置
                default_config = DEFAULT_CONFIG.copy()
                self._merge_config(default_config, self._config)
                self._config = default_config

                # 保存更新后的配置
                self.save_config()
                print("配置已更新并保存")

        except Exception as e:
            print(f"确保配置完整性失败: {e}")

    def _merge_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        递归合并配置字典

        Args:
            target: 目标字典（会被修改）
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value


class DeviceProfileManager:
    """设备配置文件管理器"""
    
    def __init__(self, profile_file: str = "device_profiles.json"):
        """
        初始化设备配置文件管理器
        
        Args:
            profile_file: 设备配置文件名
        """
        self.profile_file = os.path.join(CONFIG_DIR, profile_file)
        self._profiles = {}
        self.load_profiles()
    
    def load_profiles(self) -> None:
        """加载设备配置文件"""
        try:
            if os.path.exists(self.profile_file):
                with open(self.profile_file, 'r', encoding='utf-8') as f:
                    self._profiles = json.load(f)
            else:
                self._profiles = self._get_default_profiles()
                self.save_profiles()
        except Exception as e:
            print(f"加载设备配置文件失败: {e}")
            self._profiles = self._get_default_profiles()
    
    def save_profiles(self) -> None:
        """保存设备配置文件"""
        try:
            os.makedirs(CONFIG_DIR, exist_ok=True)
            with open(self.profile_file, 'w', encoding='utf-8') as f:
                json.dump(self._profiles, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存设备配置文件失败: {e}")
    
    def get_profile(self, device_name: str) -> Optional[Dict[str, Any]]:
        """
        获取设备配置
        
        Args:
            device_name: 设备名称
            
        Returns:
            设备配置字典
        """
        return self._profiles.get(device_name)
    
    def add_profile(self, device_name: str, profile: Dict[str, Any]) -> None:
        """
        添加设备配置
        
        Args:
            device_name: 设备名称
            profile: 设备配置
        """
        self._profiles[device_name] = profile
    
    def _get_default_profiles(self) -> Dict[str, Any]:
        """获取默认设备配置"""
        return {
            "default": {
                "test_type": "ltpo",
                "brightness_range": [1, 255],
                "fps_modes": [1, 60, 120],
                "gallery_package": "com.vivo.gallery",
                "remote_path": "/sdcard/"
            }
        }


# 全局配置管理器实例
config_manager = ConfigManager()
device_profile_manager = DeviceProfileManager()
